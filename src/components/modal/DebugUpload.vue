<template>
  <div>
    <h3>GIF 上传调试组件</h3>
    <input 
      type="file" 
      @change="handleFileSelect" 
      accept="image/gif,image/jpeg,image/jpg,image/png,image/bmp,image/webp"
    />
    <div v-if="isUploading">上传中...</div>
    <div v-if="uploadResult">
      <p>上传结果: {{ uploadResult }}</p>
      <img v-if="uploadResult" :src="uploadResult" alt="上传的图片" style="max-width: 200px;" />
    </div>
    <div v-if="error" style="color: red;">
      错误: {{ error }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { stsTokenApi } from "@/api/answer.js";

const isUploading = ref(false);
const uploadResult = ref('');
const error = ref('');

const handleFileSelect = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  console.log('选择的文件:', file.name, file.type, file.size);
  
  isUploading.value = true;
  error.value = '';
  uploadResult.value = '';
  
  try {
    // 1. 获取 STS Token
    console.log('1. 获取 STS Token...');
    const response = await stsTokenApi();
    console.log('STS Token 响应:', response);
    
    const ext = file.name.split('.').pop();
    const fileName = response.data.fileName;
    
    // 2. 构建上传参数
    const dataObj = {
      policy: response.data.policy,
      signature: response.data.signature,
      key: response.data.dir + `/${fileName}.${ext}`,
      ossaccessKeyId: response.data.accessKeyId,
      dir: response.data.dir,
      host: response.data.host,
      success_action_status: '200',
    };
    
    console.log('2. 上传参数:', dataObj);
    
    // 3. 构建 FormData
    const formData = new FormData();
    Object.keys(dataObj).forEach((key) => {
      if (dataObj[key]) {
        formData.append(key, dataObj[key]);
      }
    });
    formData.append('file', file);
    
    console.log('3. FormData 构建完成');
    
    // 4. 上传文件
    console.log('4. 开始上传...');
    const uploadResponse = await fetch('https://images2.kkzhw.com', {
      method: 'POST',
      body: formData,
    });
    
    console.log('5. 上传响应状态:', uploadResponse.status);
    
    if (uploadResponse.ok) {
      const result = await uploadResponse.json();
      console.log('6. 上传成功:', result);
      
      const url = `${dataObj.host}/${dataObj.key}`;
      uploadResult.value = url;
      console.log('7. 生成的 URL:', url);
    } else {
      const errorText = await uploadResponse.text();
      throw new Error(`上传失败: ${uploadResponse.status} - ${errorText}`);
    }
    
  } catch (err) {
    console.error('上传过程出错:', err);
    error.value = err.message;
  } finally {
    isUploading.value = false;
  }
};
</script>

<style scoped>
div {
  margin: 10px 0;
}
input[type="file"] {
  margin: 10px 0;
}
</style>
