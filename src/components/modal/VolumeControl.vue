<template>
  <div v-if="isVisible" class="volume-control-modal">
    <div class="volume-icon">
      <img :src="volumeIcon" alt="音量" />
    </div>
    <div class="volume-bar-container">
      <div class="volume-bar">
        <div
          class="volume-fill"
          :style="{ width: `${(volume / 10) * 100}%` }"
        ></div>
      </div>
    </div>
    <div class="volume-level">{{ volume }}</div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";

// 导入音量图标ß
import volumeHighIcon from "../../assets/volume-high.svg";
import volumeMediumIcon from "../../assets/volume-medium.svg";
import volumeLowIcon from "../../assets/volume-low.svg";
import volumeMuteIcon from "../../assets/volume-mute.svg";

const props = defineProps({
  volume: {
    type: Number,
    default: 5,
    validator: function (value) {
      return value >= 0 && value <= 10;
    },
  },
});

const emit = defineEmits(["volumeChange"]);

const isVisible = ref(false);
const hideTimer = ref(null);

// 根据音量级别选择图标
const volumeIcon = computed(() => {
  if (props.volume === 0) return volumeMuteIcon;
  if (props.volume <= 3) return volumeLowIcon;
  if (props.volume <= 7) return volumeMediumIcon;
  return volumeHighIcon;
});

// 显示音量控制面板
const show = function () {
  isVisible.value = true;

  // 清除之前的定时器
  if (hideTimer.value) {
    clearTimeout(hideTimer.value);
  }

  // 2秒后自动隐藏
  hideTimer.value = setTimeout(function () {
    isVisible.value = false;
  }, 2000);
};

// 隐藏音量控制面板
const hide = function () {
  isVisible.value = false;
  if (hideTimer.value) {
    clearTimeout(hideTimer.value);
    hideTimer.value = null;
  }
};

// 键盘事件处理
const handleKeyDown = function (event) {
  // 检查是否按下了 Alt 键
  if (event.altKey) {
    let newVolume = props.volume;
    console.log(event.key, 321321321);
    if (event.key === "=" || event.key === "+" || event.key === "≠") {
      // Alt + = 或 Alt + + 增加音量
      event.preventDefault();
      newVolume = Math.min(10, props.volume + 1);
      emit("volumeChange", newVolume);
      show();
    } else if (event.key == "–") {
      // Alt + - 减少音量
      event.preventDefault();
      newVolume = Math.max(0, props.volume - 1);
      emit("volumeChange", newVolume);
      show();
    }
  }
};

onMounted(function () {
  // 添加全局键盘事件监听
  document.addEventListener("keydown", handleKeyDown);
});

onUnmounted(function () {
  // 移除键盘事件监听
  document.removeEventListener("keydown", handleKeyDown);
  if (hideTimer.value) {
    clearTimeout(hideTimer.value);
  }
});

// 暴露方法供父组件调用
defineExpose({
  show,
  hide,
});
</script>

<style scoped>
.volume-control-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.85);
  border-radius: 12px;
  padding: 20px 30px;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 10000;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 200px;
}

.volume-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-icon img {
  width: 100%;
  height: 100%;
  /* SVG 图标已经是白色，不需要 filter */
}

.volume-bar-container {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.volume-bar {
  width: 100%;
  height: 100%;
  position: relative;
}

.volume-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50 0%, #8bc34a 50%, #cddc39 100%);
  border-radius: 3px;
  transition: width 0.2s ease;
}

.volume-level {
  color: white;
  font-size: 14px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

/* 动画效果 */
.volume-control-modal {
  animation: fadeInScale 0.2s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>
