<template>
  <div class="versionClass">版本号：{{ version }} 2025.08.08</div>
  <div v-if="list.length" class="indexContent">
    <div
      v-for="item in list"
      :key="item.id"
      class="labelBox"
      @click="goPrview(item)"
      @contextmenu.prevent="showMenu($event, item)"
    >
      <!--
    @click="showMenu($event, item)"
    @click="goPrview(item)"
    @contextmenu.prevent="showMenu($event, item)" -->
      <img
        class="labelImg"
        src="https://img2.baidu.com/it/u=2215736893,1692312373&fm=253&fmt=auto&app=138&f=JPEG?w=504&h=500"
        alt=""
      />
      {{ item.categoryName }}
      <!-- <div style="position: absolute; top: 0px; right: 0px">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-demo"
          mode="horizontal"
          menu-trigger="hover"
          :ellipsis="false"
          @select="handleSelect(item)"
          @click.stop
        >
          <el-sub-menu index="2">
            <template #title>...</template>
            <el-menu-item index="2-1">关卡配置</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div> -->
    </div>
  </div>
  <div
    v-if="showEmpty"
    style="
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 50px;
    "
  >
    <img style="width: 200px; height: 200px" src="../assets/null.png" alt="" />
    <p>请先添加题库</p>
  </div>
  <div
    v-if="!isCdkFlag"
    style="
      position: absolute;
      z-index: 9999;
      right: 22px;
      bottom: 100px;
      cursor: pointer;
    "
    @click="goSetting"
  >
    <img style="width: 36px; height: 36px" src="../assets/user.png" alt="" />
  </div>
  <div
    v-if="!isCdkFlag"
    style="
      position: absolute;
      z-index: 9999;
      right: 20px;
      bottom: 50px;
      cursor: pointer;
    "
    @click="goQuestions"
  >
    <el-icon size="40" color="#dbdbdb"><Tools /></el-icon>
  </div>
  <!-- v-if="isStart" -->
  <sdkModal @successBtn="successBtn" :isCdkFlag="isCdkFlag" />
  <TittopText ref="tittopTextRef" />
</template>

<script setup>
import { invoke } from "@tauri-apps/api/core";
import { Tools } from "@element-plus/icons-vue";
import { generateAuthHeaders } from "../utils/utils";
import sdkModal from "../components/modal/sdkModal.vue";
import TittopText from "../components/modal/tittopText.vue";
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import axios from "axios";
import { setQuizBind, getCategoriesList } from "../api/answer";
// const store = new Storage();
const router = useRouter();
let list = ref([]);
let isCdkFlag = ref(false);
let showEmpty = ref(false);
function goQuestions() {
  router.push({ path: "/answerList" });
}
function goSetting() {
  router.push({ path: "/userSetting" });
}

function goPrview(item) {
  let localList = item.config;
  if (localList) {
    let clearIng = localStorage.getItem(item.categoryName + "clearing");
    if (clearIng) {
      localStorage.removeItem(item.categoryName + "clearing");
    }
    localStorage.removeItem("groupList");
    router.push({
      path: "/content",
      query: { name: item.categoryName, isIndex: true, config: item.config },
    });
  } else {
    tittopTextRef.value?.open("请先生成关卡");
  }
}
const tittopTextRef = ref(null);

async function getContentList() {
  try {
    // const res = await axios.get(
    //   "https://api2.kkzhw.com/mall-portal/openapi/quiz/categories",
    //   {
    //     headers: await generateAuthHeaders(),
    //   }
    // );
    getCategoriesList().then((res) => {
      if (res.code === 200 && Array.isArray(res.data)) {
        // 将接口返回的分类名转为list格式
        list.value = res.data;
        if (res.data.length) {
          showEmpty.value = false;
        } else {
          showEmpty.value = true;
        }
      } else {
        showEmpty.value = true;
      }
    });
  } catch (e) {
    console.error("获取分类失败", e);
  }
}
function successBtn() {
  // isCdkFlag.value=false
  let deviceCode1 = localStorage.getItem("deviceCode1");
  let skdToken1 = localStorage.getItem("skdToken1");
  setQuizBind({ cdkey: skdToken1, device: deviceCode1 }).then((res) => {
    console.log(res, 1111);
    // 判断返回的code是否为200，表示绑定成功
    if (res.code === 200) {
      // 绑定成功后可以刷新列表或做其他操作
      isCdkFlag.value = false;
      getContentList();
    } else {
      // 绑定失败，弹窗提示
      isCdkFlag.value = true;
      localStorage.removeItem("skdToken1");
      tittopTextRef.value?.open(res.message || "请输入正确的CDK");
    }
  });
}
function handleSelect(e) {
  router.push({ path: "/setting", query: { name: e } });
}
function showMenu(e, item) {
  router.push({
    path: "/setting",
    query: {
      name: item.categoryName,
      id: item.id,
      config: item.config,
      cacheExpireTime: item.cacheExpireTime,
    },
  });
}
const version = ref("");
onMounted(async () => {
  // localStorage.setItem("skdToken1", 'rxdfzjxhohiqvsff');
  // 首先判断本地存储是否存在skdToken1字段
  const skdToken1 = localStorage.getItem("skdToken1");
  if (!skdToken1) {
    // 如果没有skdToken1，则弹出CDK激活弹窗
    isCdkFlag.value = true;
    return;
  }
  getContentList();
  version.value = await invoke("get_app_version");
});
</script>

<style scoped>
.indexContent {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}
.labelBox {
  cursor: pointer;
  width: 100%;
  height: 80px;
  background: #fdf8f8;
  margin-bottom: 10px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding-left: 30px;
  box-sizing: border-box;
  font-size: 18px;
  font-weight: bold;
  position: relative;
}
.labelBox:hover {
  background: #99f1d3;
}
.labelImg {
  width: 46px;
  height: 46px;
  margin-right: 10px;
}
.el-menu--horizontal.el-menu {
  background: transparent !important;
  border: none !important;
}
.el-menu--horizontal.el-menu:hover,
.el-menu--horizontal.el-menu:focus,
.el-menu--horizontal.el-menu:active {
  background: transparent !important;
}
.el-menu--horizontal.el-menu .el-menu-item:hover,
.el-menu--horizontal.el-menu .el-menu-item.is-active,
.el-menu--horizontal.el-menu .el-sub-menu__title:hover {
  background: transparent !important;
}
:deep(.el-sub-menu__icon-arrow) {
  display: none !important;
}
</style>
<style>
.el-sub-menu__title:hover {
  background: transparent !important;
}
.el-menu {
  height: 46px;
}
.el-sub-menu__title {
  color: #000 !important;
  border: none !important;
}
.versionClass {
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 14px;
  color: #bababa;
}
</style>
