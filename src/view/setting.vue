<template>
  <div class="headerContent1">
    <img
      @click="goBack"
      src="../assets/cancel.png"
      alt="返回"
      class="back-btn1"
    />
    <span style="color: #000">{{ guanqiaName }}关卡配置</span>
  </div>
  <div class="container">
    <div class="app-container">
      <!-- 左侧关卡列表 -->
      <el-card class="levels-panel" shadow="hover">
        <template #header>
          <div class="panel-title">
            <!-- <el-icon><List /></el-icon> -->
            <span>关卡列表</span>
          </div>
          <div style="margin-top: 10px; cursor: pointer">
            <!-- <el-tag @click="scList(1)" type="primary">新手模式</el-tag> -->
            <el-tag
              @click="scList(2)"
              style="margin-left: 10px; margin-right: 10px"
              type="warning"
              >标准模式（3-3-3-1）</el-tag
            >
            <!-- <el-tag @click="scList(3)" type="danger">挑战模式</el-tag>
            <el-tag @click="scList(4)" style="background: #FFD700;margin-left: 10px;color: #a38a06;" type="danger">史诗模式</el-tag> -->
          </div>
        </template>

        <div class="levels-list">
          <div
            v-for="(level, index) in levels"
            :key="level.id"
            class="level-item"
            :class="{ active: selectedLevelId === level.id }"
            @click="selectLevel(level.id)"
          >
            <div
              class="level-icon"
              :style="{ background: difficultyMap[level.difficulty].color }"
            >
              <img
                style="width: 30px; height: 30px; border-radius: 50%"
                :src="getIcon(level)"
                :alt="difficultyMap[level.difficulty].name"
              />
            </div>
            <div class="level-info">
              <div class="level-name">{{ level.name }}</div>
              <div class="level-difficulty">
                难度：{{ difficultyMap[level.difficulty].name }}
              </div>
            </div>
            <el-button
              v-if="levels.length > 1"
              @click.stop="settingDelete(index)"
              class="settingDeleteBtn"
              type="danger"
              :icon="Delete"
              circle
            />
          </div>
        </div>

        <el-button type="success" @click="addLevel" class="add-level-btn">
          <el-icon><Plus /></el-icon>
          添加新关卡
        </el-button>
      </el-card>

      <!-- 右侧配置面板 -->
      <div class="config-panel">
        <el-card class="config-panel" shadow="hover" style="margin-bottom: 20px;">
          <template #header>
            <div class="panel-title">
              <!-- <el-icon><Setting /></el-icon> -->
              <span>全局配置</span>
            </div>
          </template>

          <!-- 基本信息配置 -->
          <div class="config-section" style="border: none">
            <div>
              每题答题后
              <el-input-number style="width: 200px;" v-model="huancunTime" :min="0" :max="99999999999">
                <template #suffix>
                  <span>小时</span>
                </template>
              </el-input-number>
              内不出现
               <el-button style="margin-left: 10px;" type="danger" @click="resetClear">
                  <el-icon><RefreshLeft /></el-icon>
                  清空答题记录
                </el-button>
            </div>
            <!-- <div class="actions">
              <el-button type="primary" @click="saveLevel">
                <el-icon><Check /></el-icon>
                保存配置
              </el-button>
              <el-button type="danger" @click="resetLevel">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </div> -->
          </div>

          <!-- 操作按钮 -->
        </el-card>
        <el-card class="config-panel" v-if="selectedLevel" shadow="hover">
          <template #header>
            <div class="panel-title">
              <!-- <el-icon><Setting /></el-icon> -->
              <span>关卡详细配置</span>
            </div>
          </template>

          <!-- 基本信息配置 -->
          <div class="config-section">
            <h3 class="section-title">
              <!-- <el-icon><InfoFilled /></el-icon> -->
              基本信息
            </h3>

            <el-form :model="selectedLevel" label-width="100px">
              <el-form-item label="关卡名称">
                <el-input
                  v-model="selectedLevel.name"
                  placeholder="请输入关卡名称"
                  :maxlength="15"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="关卡描述">
                <el-input
                  v-model="selectedLevel.description"
                  placeholder="请输入关卡描述"
                />
              </el-form-item>

              <!-- <el-form-item label="关卡图标">
              <el-select v-model="selectedLevel.icon" placeholder="请选择图标">
                <el-option label="简单" value="Sunny">
                  <el-icon><Sunny /></el-icon>
                  <span style="margin-left: 8px">简单</span>
                </el-option>
                <el-option label="中等" value="Cloudy">
                  <el-icon><Cloudy /></el-icon>
                  <span style="margin-left: 8px">中等</span>
                </el-option>
                <el-option label="困难" value="Lightning">
                  <el-icon><Lightning /></el-icon>
                  <span style="margin-left: 8px">困难</span>
                </el-option>
              </el-select>
            </el-form-item> -->
            </el-form>
          </div>

          <!-- 题目设置 -->
          <div class="config-section" style="border: none">
            <h3 class="section-title">题目设置</h3>

            <el-form :model="selectedLevel" label-width="100px">
              <el-form-item label="题目数量">
                <div class="slider-container">
                  <el-slider
                    v-model="selectedLevel.questionsNum"
                    :min="5"
                    :max="30"
                    show-input
                    style="flex: 1; margin-right: 20px"
                  />
                </div>
              </el-form-item>

              <el-form-item label="难度级别">
                <div class="slider-container">
                  <el-slider
                    :show-tooltip="false"
                    v-model="selectedLevel.difficulty"
                    :min="1"
                    :max="4"
                    :marks="{ 1: '简单', 2: '中等', 3: '困难', 4: '史诗' }"
                    style="flex: 1; margin-right: 20px"
                  />
                </div>

                <div class="difficulty-visual">
                  <div class="difficulty-bar easy"></div>
                  <div class="difficulty-bar medium"></div>
                  <div class="difficulty-bar hard"></div>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮 -->
        </el-card>
      </div>
    </div>
    <div class="actions">
      <el-button type="primary" @click="saveLevel">
        <el-icon><Check /></el-icon>
        保存配置
      </el-button>
      <el-button type="danger" @click="resetLevel">
        <el-icon><RefreshLeft /></el-icon>
        重置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { updateCategory,clearCache } from "@/api/answer";
const route = useRoute();
const router = useRouter();
import { invoke } from "@tauri-apps/api/core";
import {
  List,
  Setting,
  InfoFilled,
  QuestionFilled,
  Plus,
  Check,
  RefreshLeft,
  Delete,
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
function goBack() {
  router.push({ path: "/" });
}
// 难度级别映射
const difficultyMap = {
  1: { name: "简单", color: "#2ecc71", icon: "Sunny" },
  2: { name: "中等", color: "#f39c12", icon: "Cloudy" },
  3: { name: "困难", color: "#e74c3c", icon: "Lightning" },
  4: { name: "史诗", color: "#edcf2d", icon: "ShiShi" },
};

// 关卡数据
const levels = ref([
  {
    id: 1,
    name: "第1关",
    description: "适合新手的简单题目，帮助熟悉答题规则",
    difficulty: 1,
    questionsNum: 10,
  },
  {
    id: 2,
    name: "第2关",
    description: "中等难度题目，巩固基础知识",
    difficulty: 2,
    questionsNum: 10,
  },
  {
    id: 3,
    name: "第3关",
    description: "挑战性题目，考验知识应用能力",
    difficulty: 3,
    questionsNum: 10,
  },
  {
    id: 4,
    name: "第4关",
    description: "史诗题目，考验知识应用能力",
    difficulty: 4,
    questionsNum: 10,
  },
]);
// 组件挂载时执行
const guanqiaName = ref("");
const  huancunTime=ref(6)
onMounted(async () => {
  guanqiaName.value = route.query.name;
  let localList = route.query.config;
  // 检查 query 是否存在 cacheExpireTime，存在则将秒转为小时
  if (route.query.cacheExpireTime) {
    huancunTime.value = Math.floor(Number(route.query.cacheExpireTime) / 3600);
  }
  if (localList != null) {
    let arr = JSON.parse(localList);
    selectedLevelId.value = arr[0].id;
    levels.value = arr;
  }
  await invoke("set_window_width", { width: 1000 });
});
onUnmounted(async () => {
  await invoke("set_window_width", { width: 500 });
});
const resetClear=()=>{
  clearCache({categoryName:guanqiaName.value}).then(res=>{
    ElMessage.success(`清空成功`);
  })
}

const scList = (e) => {
  let newLevels = [];
  // 难度穿插数组
  let easyMediumPattern = [1, 1, 1, 2, 1, 2, 1, 1, 1, 2];
  // let mediumHardPattern = [1, 2, 2, 1, 2, 2, 2, 3, 2, 3];
  let mediumHardPattern = [1, 1, 1, 2, 2, 2, 3, 3, 3, 4];
  let hardPattern = [3, 3, 3, 3, 3, 3, 3, 3, 3, 3];
  let shishiPattern = [1, 1, 1, 2, 2, 2, 3, 3, 3, 4];

  for (let i = 1; i <= 10; i++) {
    let diff = 1;
    let desc = "";
    if (e === 1) {
      // 新手模式：简单和中等难度穿插
      diff = easyMediumPattern[i - 1];
      desc = `新手模式第${i}关，难度${difficultyMap[diff].name}`;
    } else if (e === 2) {
      // 标准模式：中等和困难难度穿插
      diff = mediumHardPattern[i - 1];
      desc = `标准模式第${i}关，难度${difficultyMap[diff].name}`;
    } else if (e === 3) {
      // 挑战模式：全部为困难
      diff = hardPattern[i - 1];
      desc = `挑战模式第${i}关，难度${difficultyMap[diff].name}`;
    } else if (e === 4) {
      // 挑战模式：全部为困难
      diff = shishiPattern[i - 1];
      desc = `史诗模式第${i}关，难度${difficultyMap[diff].name}`;
    }
    newLevels.push({
      id: i,
      name: `第${i}关`,
      description: desc,
      difficulty: diff,
      questionsNum: 10,
    });
  }
  levels.value = newLevels;
  selectedLevelId.value = 1;
};

// 当前选中的关卡ID
const selectedLevelId = ref(1);

// 当前选中的关卡
const selectedLevel = computed(() => {
  return levels.value.find((level) => level.id === selectedLevelId.value);
});

// 选择关卡
const selectLevel = (id) => {
  selectedLevelId.value = id;
};
//删除关卡
const settingDelete = (e) => {
  console.log(e, 1111);

  levels.value.splice(e, 1);
};
// 添加新关卡
const addLevel = () => {
  const newId =
    levels.value.length > 0
      ? Math.max(...levels.value.map((l) => l.id)) + 1
      : 1;

  levels.value.push({
    id: newId,
    name: `第${newId}关：新关卡`,
    description: "新关卡描述",
    difficulty: 1,
    questionsNum: 10,
  });

  selectedLevelId.value = newId;
  ElMessage.success("新关卡添加成功！");
};

// 保存关卡配置
const saveLevel = () => {
  let mpHa = {
    1: "简单",
    2: "中等",
    3: "困难",
    4: "史诗",
  };
  levels.value.forEach((item) => {
    item.difficultyName = mpHa[item.difficulty];
  });
  if (levels.value.length == 0) {
    ElMessage.error(`最少保留一个关卡`);
    return;
  }
  // guanqiaName
  let params = {
    categoryName: guanqiaName.value,
    config: JSON.stringify(levels.value),
    cacheExpireTime: huancunTime.value * 3600
  };
  updateCategory(route.query.id, params).then((res) => {
    ElMessage.success(`关卡配置已保存！`);
  });
  // // 假设 guanqiaName 是一个字符串变量，作为本地存储的key
  // let key = guanqiaName.value;
  // let levelsStr = JSON.stringify(levels.value);

  // localStorage.setItem(key, levelsStr);
};

// 重置当前关卡配置
const resetLevel = async () => {
  try {
    await ElMessageBox.confirm("确定要重置当前关卡的配置吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    levels.value = [
      {
        id: 1,
        name: "第1关",
        description: "适合新手的简单题目，帮助熟悉答题规则",
        difficulty: 1,
        questionsNum: 10,
      },
      {
        id: 2,
        name: "第2关",
        description: "中等难度题目，巩固基础知识",
        difficulty: 2,
        questionsNum: 10,
      },
      {
        id: 3,
        name: "第3关",
        description: "挑战性题目，考验知识应用能力",
        difficulty: 3,
        questionsNum: 10,
      },
      {
        id: 4,
        name: "第4关",
        description: "史诗难度题目，考验知识应用能力",
        difficulty: 4,
        questionsNum: 10,
      },
    ];
    // const defaultLevel = {
    //   id: selectedLevel.value.id,
    //   name: `第${selectedLevel.value.id}关`,
    //   description: "关卡描述",
    //   difficulty: 1,
    //   questionsNum: 10,
    //   timePerQuestion: 45,
    //   totalTimeLimit: 8,
    //   icon: "Sunny",
    // };

    // const index = levels.value.findIndex(
    //   (l) => l.id === selectedLevel.value.id
    // );
    // if (index !== -1) {
    //   levels.value[index] = { ...defaultLevel };
    // }

    ElMessage.success("关卡配置已重置！");
  } catch (error) {
    // 用户取消操作
  }
};
// 由于直接返回字符串路径不会被Vite/webpack正确解析图片资源，建议使用import.meta.glob引入图片资源
const icons = import.meta.glob("../assets/*.png", {
  eager: true,
  import: "default",
});

const getIcon = (level) => {
  // 假设difficulty为数字，图片命名为 1.png、2.png 等
  const path = `../assets/${level.difficulty}.png`;
  return icons[path] || "";
};
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0px 20px 20px 20px;
}

.app-container {
  display: flex;
  gap: 25px;
}

.levels-panel {
  flex: 1;
  min-height: 600px;
}

.config-panel {
  flex: 2;
  min-height: 100px;
}
.config-panel2 {
  flex: 2;
  min-height: 100px;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
}

.levels-list {
  max-height: 450px;
  overflow-y: auto;
  margin-bottom: 20px;
  /* 隐藏滚动条，兼容所有主流浏览器 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE和Edge */
}
.levels-list::-webkit-scrollbar {
  display: none; /* Chrome、Safari 和 Opera */
}
.level-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
}
.level-item:first-child {
  margin-top: 10px;
}

.level-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-2px);
}

.level-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.level-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.level-info {
  flex: 1;
}

.level-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
}

.level-difficulty {
  font-size: 14px;
  color: #909399;
}

.add-level-btn {
  width: 100%;
}

.config-section {
  margin-bottom: 0px;
  padding-bottom: 0px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
}

.slider-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.difficulty-visual {
  display: flex;
  gap: 5px;
  margin-top: 15px;
  height: 8px;
}

.difficulty-bar {
  flex: 1;
  border-radius: 4px;
}

.difficulty-bar.easy {
  background: linear-gradient(to right, #2ecc71, #27ae60);
}

.difficulty-bar.medium {
  background: linear-gradient(to right, #f39c12, #e67e22);
}

.difficulty-bar.hard {
  background: linear-gradient(to right, #e74c3c, #c0392b);
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}
.headerContent1 {
  display: flex;
  align-items: center;
  padding: 20px 20px 10px 20px;
  font-size: 20px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}
.back-btn1 {
  width: 20px;
  height: 20px;
  margin-right: 15px;
  cursor: pointer;
}
.settingDeleteBtn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}
</style>
