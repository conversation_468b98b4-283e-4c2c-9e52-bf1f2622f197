<template>
  <div class="pageContent">
    <div class="pageHead">
      <img @click="backModalBtn" src="../assets/cancel.png" alt="返回" />
      {{ answerPageName }}
    </div>
    <div class="pageHeadBtns">
      <div class="btnsAnswerClass">
        <div v-if="arrIndex != 1" @click="topAnswerBtn" class="topAnswer">
          上一题
        </div>
      </div>
      <div>当前答对{{ getanswerNumber() }}题</div>
      <div class="btnsAnswerClass">
        <div
          v-if="arrIndex != pageListArr.length"
          @click="bottomAnswerBtn"
          class="bottomAnswer"
        >
          下一题
        </div>
      </div>
    </div>
    <div class="pageTitle">
      第{{groupNumber}}关（{{ arrIndex }}/{{ pageListArr.length }}）
    </div>
    <div class="pageLabelContent">
      <span style="font-size: 22px;">{{ pageListArr[arrIndex - 1]?.content }}</span>
      <span class="pageTag">{{ pageListArr[arrIndex - 1].questionType }}</span>
      <!-- <div class="answerTitle">{{ pageListArr[arrIndex - 1]?.content }}</div> -->
    </div>
    <div style="display: flex; flex-direction: column">
      <div class="pageImgBox">
        <img
          v-if="pageListArr[arrIndex - 1] && pageListArr[arrIndex - 1].imageUrl"
          :src="pageListArr[arrIndex - 1].imageUrl"
          alt=""
        />
        <!-- 选择题 v-if="questionType == '选择题'"-->

        <div v-if="pageListArr[arrIndex - 1].questionType == '选择题'">
          <div
            v-for="(item, index) in pageListArr[arrIndex - 1].options"
            :class="[
              isLogDaAn && index === pageListArr[arrIndex - 1].correctAnswer
                ? 'selectTypeSuccess'
                : '',
            ]"
            :key="index"
            class="selectType"
            @click="
              !pageListArr[arrIndex - 1].isStartBtn &&
                selectQuestionNumber(item)
            "
          >
            {{ String.fromCharCode(65 + index) }}、{{ item }}
          </div>
        </div>
      </div>
      <div
        style="width: 100%; height: 1px; background: #dfdede; margin-top: 10px"
      ></div>
      <div
        class="successTextBox"
        v-if="pageListArr[arrIndex - 1].isLogDaAn"
        :style="{
          height: (!pageListArr[arrIndex - 1].isStartBtn && !pageListArr[arrIndex - 1].isOld) ? '145px' : 'auto'
        }"
      >
        <div class="answerSuccess">
          正确答案：<span class="answerSuccessSpan">{{
            pageListArr[arrIndex - 1].correctAnswer || ""
          }}</span>
        </div>
        <div class="answerAnalysis">答案解析:</div>
        <div class="answerAnalysisContent">
          {{
            pageListArr[arrIndex - 1].answerAnalysis
              ? pageListArr[arrIndex - 1].answerAnalysis
              : "无"
          }}
        </div>
      </div>
      <div
        class="btnBox"
        v-if="
          !pageListArr[arrIndex - 1].isStartBtn &&
          !pageListArr[arrIndex - 1].isOld
        "
      >
        <div
          class="countdownBoxTop"
          style="display: flex; align-items: center; justify-content: center"
          v-if="!pageListArr[arrIndex - 1].isLogDaAn"
        >
          <div
            class="countdownBox"
            :class="[
              countdownNum > 0 && countdownNum < 4 ? 'countdownBoxAm' : '',
            ]"
          ></div>
          <div class="countdownNumText">
            {{ countdownNum }}
          </div>
        </div>

        <div
          v-if="!pageListArr[arrIndex - 1].isLogDaAn"
          @click="logDaAnBtn"
          class="answerPageBtn myBtn2 logBtn btnStyleAm"
        >
          查看答案
        </div>
        <div
          @click="clickCorrect"
          class="answerPageBtn myBtn2 successBtn btnStyleAm"
        >
          <span> 回答正确</span>
        </div>
        <div
          @click="clickIncorrect"
          class="answerPageBtn myBtn2 errorBtn btnStyleAm"
        >
          回答错误
        </div>
      </div>
    </div>
  </div>
  <div v-if="pageListArr[arrIndex - 1].isStartBtn" class="startAnswerBtnBox">
    <div @click="startBtn" class="myBtn startAnswerBtn btnStyleAm">
      开始答题
    </div>
  </div>
  <audio :src="audioRef"></audio>
  <audio :src="successAudioRef"></audio>
  <audio :src="errorAudioRef"></audio>
  <ModalTip
    v-if="isBackModal"
    @submit="backSubmitBtn"
    @cancel="baclCancelBtn"
  />
  <Tittop v-if="isTittopFlag" :type="isTittopType" />
</template>
<script setup>
import audioFile from "../assets/downNum1.mp3";
import audioFileSuccess from "../assets/success.mp3";
import audioFileError from "../assets/error.mp3";
import ModalTip from "../components/modal/index.vue";
import Tittop from "../components/modal/tittop.vue";
import { ref, onMounted, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { setTimeCache } from '../api/answer'
const route = useRoute();
const router = useRouter();
let arrIndex = ref(1);
let groupNumber=ref(1)
let pageListArr = reactive([
  {
    id: 8,
    gameCategory: "逆水寒手游",
    questionType: "问答题",
    content: "家可以点击任务直接自动寻路到目标位置。家可以点击任务直接自动寻路到目标位置。",
    correctAnswer:
      "帮会系统主要功能包括：\n1.帮会建设和升级\n2.帮会任务和活动\n3.帮会商店兑换\n4.帮会战和领地争夺\n5.帮会成员互助和交流",
    answerAnalysis:
      "帮会系统是逆水寒手游的重要社交玩法，提供了丰富的团队合作内容。",
    createTime: "2025-07-17T06:19:28.000+00:00",
  },
  {
    id: 6,
    gameCategory: "逆水寒手游",
    questionType: "判断题",
    content: "逆水寒手游中是否有自动寻路功能？",
    correctAnswer: "是",
    answerAnalysis:
      "逆水寒手游提供了便捷的自动寻路功能，玩家可以点击任务直接自动寻路到目标位置。",
    createTime: "2025-07-17T06:19:28.000+00:00",
  },
  {
    id: 10,
    gameCategory: "逆水寒手游",
    questionType: "选择题",
    content: "逆水寒手游中主要的货币是什么？",
    options: ["金币", "银两", "元宝", "铜钱"],
    correctAnswer: "银两",
    answerAnalysis: "银两是逆水寒手游中的主要货币，用于购买装备、道具等。",
    createTime: "2025-07-17T06:19:28.000+00:00",
  },
  {
    id: 9,
    gameCategory: "逆水寒手游",
    questionType: "判断题",
    content: "逆水寒手游是否支持多人组队？",
    correctAnswer: "是",
    answerAnalysis:
      "逆水寒手游支持多人组队，玩家可以与好友一起完成任务和副本。",
    createTime: "2025-07-17T06:19:28.000+00:00",
  },
  {
    id: 7,
    gameCategory: "逆水寒手游",
    questionType: "选择题",
    content: "逆水寒手游中哪个门派擅长远程攻击？",
    options: ["神相", "血河", "九灵", "铁衣"],
    correctAnswer: "血河",
    answerAnalysis:
      "血河门派是逆水寒手游中的远程输出门派，擅长使用弓箭进行远程攻击。",
    createTime: "2025-07-17T06:19:28.000+00:00",
  },
]);
let isBackModal = ref(false);

let countdownNum = ref(10);
let answerPageName = ref("");
let questionType = ref(""); //题类型
let isLogDaAn = ref(false); //是否查看答案
let timer = null;
// const audioRef = ref(null);
const audioRef = ref(new Audio(audioFile));
function startBtn() {
  //   let arrObj = pageListArr[arrIndex.value - 1];
  //   arrObj.isStartBtn = false;
  const index = arrIndex.value - 1;
  pageListArr[index] = { ...pageListArr[index], isStartBtn: false };
  audioRef.value.volume = 0.5;
  audioRef.value.play();
  startTimeFun();
  // console.log(pageListArr[index].id,1111222);
  setTimeCache({categoryName:answerPageName.value,questionId:pageListArr[index].id});
}
function startTimeFun() {
  timer = setInterval(() => {
    if (countdownNum.value > 0) {
      countdownNum.value--;
    }
  }, 1000);
}
//是否查看答案事件
function logDaAnBtn() {
  //   isLogDaAn.value = true;
  let arrObj = pageListArr[arrIndex.value - 1];
  arrObj.isLogDaAn = true;
  audioRef.value.pause();
  audioRef.value.currentTime = 0;
}
function backSubmitBtn() {
  isBackModal.value = false;
  router.back();
}
function baclCancelBtn() {
  // 仅关闭弹窗
  isBackModal.value = false;
}
function backModalBtn() {
  isBackModal.value = true;
}
onMounted(() => {
  answerPageName.value = route.query.name;
  groupNumber.value=route.query.groupNumber
  getPageDataList();
});
async function getPageDataList() {

  const arr = JSON.parse(route.query.questions || "[]");

  // 清空原数组并填充新数据（保持响应性）
    pageListArr.splice(
      0,
      pageListArr.length,
      ...arr.map((item) => ({
        ...item,
        incorrectAnswerFlag: false,
        isLogDaAn: false,
        isStartBtn: true,
        isOld: false,
      }))
    );

  arrIndex.value = 1;
}
let isTittopFlag = ref(false);
let isTittopType = ref("");
//回答正确
const successAudioRef = ref(new Audio(audioFileSuccess));
function clickCorrect(item) {
  let arrObj = pageListArr[arrIndex.value - 1];
  isTittopFlag.value = true;
  arrObj.incorrectAnswerFlag = true;
  arrObj.isOld = true;
  arrObj.isLogDaAn = true;
  successAudioRef.value.play();
  successAudioRef.value.volume = 0.5;
  isTittopType.value = "success";
  setTimeout(() => {
    reset();
  }, 500);
}
//回答错误
const errorAudioRef = ref(new Audio(audioFileError));
function clickIncorrect(item) {
  let arrObj = pageListArr[arrIndex.value - 1];
  isTittopFlag.value = true;
  arrObj.incorrectAnswerFlag = false;
  arrObj.isOld = true;
  arrObj.isLogDaAn = true;
  errorAudioRef.value.play();
  errorAudioRef.value.volume = 0.5;
  isTittopType.value = "error";
  setTimeout(() => {
    reset();
  }, 500);
}
//选择题点击
function selectQuestionNumber(item) {
  //   let arrObj = pageListArr[arrIndex.value - 1];
  //   arrObj.beenSelected = true;
  //   // 判断选择是否正确
  //   if (arrObj.options[arrObj.questionNumber] !== item) {
  //     arrObj.incorrectAnswer = item;
  //   } else {
  //     arrObj.incorrectAnswer = "";
  //   }
}
function reset() {
  if (timer) {
    clearInterval(timer);
    timer = null;
    audioRef.value.pause();
    audioRef.value.currentTime = 0;
  }

  let arrObj = pageListArr[arrIndex.value - 1];
  arrObj.isLogDaAn = true;
  countdownNum.value = 10;
  isTittopFlag.value = false;
  // // 检查前面题是否全部答完
  // let unfinishedIndex = pageListArr.findIndex((item, idx) => idx < arrIndex.value - 1 && !item.isLogDaAn);
  // if (unfinishedIndex !== -1) {
  //   console.log(11111111);
  //   return
  //   // 有未答完的题目，进行提示
  //   isTittopFlag.value = true;
  //   isTittopType.value = "error";
  //   // 这里可以自定义提示内容，比如弹窗或顶部提示
  //   // 例如：Tittop 组件内根据 isTittopType.value 显示“请先完成前面的题目”
  //   setTimeout(() => {
  //     isTittopFlag.value = false;
  //   }, 1500);
  //   return;
  // }

  // 检查是否是最后一题
  if (arrIndex.value == pageListArr.length) {
    // 跳转到结算页面，传递答对题数和总题数
    setTimeout(() => {
      router.push({
        path: "/clearing",
        query: {
          correctCount: getanswerNumber(),
          totalCount: pageListArr.length,
          name: answerPageName.value,
          groupNumber:groupNumber.value
        },
      });
    }, 500);
    return;
  }

  arrIndex.value++;
}
function getanswerNumber() {
  let num = 0;
  pageListArr.forEach((item) => {
    if (item.incorrectAnswerFlag) {
      num++;
    }
  });
  return num;
  //   for (let index = 0; index < arrIndex.value; index++) {
  //     if (pageListArr[index].incorrectAnswerFlag) {
  //       num = num + 10;
  //     }
  //   }
  //   return num;
}
function anSwerBtnReset() {
  if (timer) {
    clearInterval(timer);
    timer = null;
    audioRef.value.pause();
    audioRef.value.currentTime = 0;
  }
  countdownNum.value = 10; //答题倒计时
}
//上一题
function topAnswerBtn() {
  anSwerBtnReset();
  arrIndex.value--;
  let arrObj = pageListArr[arrIndex.value - 1];
  console.log(arrObj, 111111);

  if (!arrObj.isStartBtn && !arrObj.isOld) {
    countdownNum.value = 10;
    audioRef.value.play();
    startTimeFun();
  }
}
//下一题
function bottomAnswerBtn() {
  anSwerBtnReset();
  arrIndex.value++;
  let arrObj = pageListArr[arrIndex.value - 1];
  if (!arrObj.isStartBtn && !arrObj.isOld) {
    countdownNum.value = 10;
    audioRef.value.play();
    startTimeFun();
  }
}
</script>
<style scoped>
.pageContent {
  width: 100%;
  height: 100vh;
  padding: 10px 20px;
  box-sizing: border-box;
}

.pageHead {
  display: flex;
  justify-content: center;
  position: relative;
  margin-bottom: 10px;
  height: 20px;
  align-items: center;
  cursor: pointer;
}

.pageHead img {
  position: absolute;
  left: 0px;
  width: 16px;
}

.pageTitle {
  width: 100%;
  font-size: 28px;
  font-weight: bold;
  color: #000;
}

.pageLabelContent {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  text-indent: 76px;
  line-height: 24px;
  margin-bottom: 10px;
  /* display: grid;
  grid-template-columns: auto 1fr; */
  /* display: flex; */

  /* display: flex;
  flex-wrap: wrap; */
  /* align-items: center; */
}

.pageTag {
  width: 70px;
  height: 26px;
  border: 2px solid #2d8cf0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2d8cf0;
  background: #fff;
  margin-right: 10px;
  position: absolute;
  top: -1px;
  left: 0px;
  text-indent: 0px;
}

.answerTitle {
  font-size: 18px;
}

.pageImgBox {
  width: 100%;
  height: 410px;
  overflow: auto;
  /* 隐藏滚动条，兼容所有主流浏览器和 Tauri */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE和Edge */
}

.pageImgBox::-webkit-scrollbar {
  display: none;
  /* Chrome、Safari、Tauri等基于Webkit的浏览器，包括Tauri */
}

.pageImgBox::-webkit-scrollbar {
  display: none;
  /* Chrome、Safari、Tauri等基于Webkit的浏览器 */
}
.successTextBox{
  height: 145px;
  overflow: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE和Edge */
}

.successTextBox::-webkit-scrollbar {
  display: none;
  /* Chrome、Safari、Tauri等基于Webkit的浏览器，包括Tauri */
}

.successTextBox::-webkit-scrollbar {
  display: none;
  /* Chrome、Safari、Tauri等基于Webkit的浏览器 */
}
.pageImgBox img {
  width: 100%;
  height: 250px;
  border-radius: 10px;
  /* object-fit: contain; */
}
.startAnswerBtnBox {
  width: 300px;
  height: 70px;
  position: absolute;
  left: 50%;
  bottom: 180px !important;
  transform: translateX(-50%);
}
.startAnswerBtn {
  width: 300px;
  height: 70px;
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px !important;
  color: #fff;
  border: none;
  background: linear-gradient(60deg, #ff741c 0%, #f5a131 100%);
}

.answerPageBtn {
  width: 90%;
  height: 50px;
  background: #000;
  margin-top: 15px;
  color: #fff;
  font-weight: bold;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
}

.btnBox {
  width: calc(100% - 40px);
  /* 兼容Tauri，减去40px以适配窗口边距 */
  position: fixed;
  bottom: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.countdownBoxTop {
  position: absolute;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -125px;
  /* 让定位基于btnBox右侧 */
  right: 4%;
}
.countdownBox {
  position: absolute;
  width: 150px;
  height: 150px;
  background-image: url("../assets/bbb.png");
  background-repeat: no-repeat;
  background-size: contain;
  /* background-position: center; */
  /* 当内容高度不足时，不重复显示背景图片 */
  /* animation: rotateShake 3s infinite; */
  display: flex;
  align-items: center;
  justify-content: center;
}
.countdownBoxAm {
  animation: rotateShake 3s infinite;
}

@keyframes rotateShake {
  0% {
    transform: rotate(0deg);
  }
  16.66% {
    transform: rotate(-5deg);
  }
  33.33% {
    transform: rotate(5deg);
  }
  50% {
    transform: rotate(-5deg);
  }
  66.66% {
    transform: rotate(5deg);
  }
  83.33% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
/* 动画加快，持续1秒 */
.countdownBox {
  animation-duration: 0.5s !important;
}

.countdownNumText {
  color: #efd83e;
  font-size: 48px;
  font-weight: 1000;
  position: relative;
  margin-top: -5px;
  /* top: -104px;
  right: 78px; */
}

/* 动画持续3秒，循环一次 */

.selectType {
  width: 100%;
  height: 30px;
  background: #f7f7f7;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  padding-left: 20px;
  box-sizing: border-box;
  border-radius: 6px;
  cursor: pointer;
}

.selectTypeSuccess {
  background: #c1f6c4 !important;
}

.selectTypeError {
  background: #fa3e40 !important;
}

.myBtn {
  cursor: pointer;
  position: absolute;
  -webkit-transition: all 0.1s ease;
  -moz-transition: all 0.1s ease;
  -ms-transition: all 0.1s ease;
  -o-transition: all 0.1s ease;
  transition: all 0.1s ease;
}
.myBtn:active {
  transform: translateY(4px);
}
.myBtn2 {
  cursor: pointer;
  position: relative;
  -webkit-transition: all 0.1s ease;
  -moz-transition: all 0.1s ease;
  -ms-transition: all 0.1s ease;
  -o-transition: all 0.1s ease;
  transition: all 0.1s ease;
}
.myBtn2:active {
  transform: translateY(4px);
}
.answerSuccess {
  color: #2d8cf0;
  font-weight: 1000;
  margin-top: 10px;
  font-size: 18px;
}
.answerSuccessSpan {
  font-size: 20px;
}
.answerAnalysis {
  font-weight: 1000;
  margin-top: 10px;
  font-size: 18px;
}
.answerAnalysisContent {
  font-size: 18px;
  font-weight: normal;
}
.pageHeadBtns {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.topAnswer {
  cursor: pointer;
  width: 100px;
  height: 30px;
  background: linear-gradient(to right, #aeefff, #2d8cf0);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 26px;
  color: #fff;
  font-size: 18px;
}
.bottomAnswer {
  cursor: pointer;
  width: 100px;
  height: 30px;
  background: linear-gradient(to right, #aeefff, #2d8cf0);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 26px;
  color: #fff;
  font-size: 18px;
}
.btnsAnswerClass {
  width: 100px;
}
.logBtn {
  width: 90%;
  height: 50px; /* 高度增加到65px，按钮更粗 */
  border: none;
  border-radius: 32px; /* 圆角半径增加，保持比例 */
  background: linear-gradient(60deg, #2d8cf0 0%, #5fd3f0 100%);
}
.errorBtn {
  width: 90%;
  height: 50px; /* 高度增加到65px，按钮更粗 */
  border: none;
  border-radius: 32px; /* 圆角半径增加，保持比例 */
  background: linear-gradient(60deg, #ff741c 0%, #f5a131 100%);
}
.successBtn {
  width: 90%;
  height: 50px; /* 高度增加到65px，按钮更粗 */
  border: none;
  border-radius: 32px; /* 圆角半径增加，保持比例 */
  background: linear-gradient(60deg, #2fbc08 0%, #48d466 100%);
}
.btnStyleAm {
  color: white;
  font-size: 20px; /* 字体增大 */
  font-weight: bold;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  /* 增强3D透视效果 */
  transform: perspective(1500px) rotateX(8deg); /* 增加透视深度和旋转角度 */
  transform-style: preserve-3d;

  box-shadow: 0 1px 4px -8px rgba(0, 0, 0, 0.4),
    /* 主阴影加深，扩散更远 */ 0 1px 2px -1px rgba(0, 0, 0, 0.25),
    /* 中层阴影 */ 0 8px 12px -3px rgba(0, 0, 0, 0.2),
    /* 近层阴影 */ inset 0 0 0 1.5px rgba(255, 255, 255, 0.3),
    /* 边缘高光加粗 */ inset 0 10px 10px 0 rgba(255, 255, 255, 0.5),
    /* 顶部强高光 */ inset 0 -18px 25px 0 rgba(0, 0, 0, 0.2);
}
/* 增强镜面效果 - 扩大高光区域 */
.btnStyleAm::before {
  content: "";
  position: absolute;
  top: 10px;
  left: 14px;
  width: 15px;
  height: 10px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  transform: skewY(-15deg);
  border-radius: 32px;
}

/* 立体边框效果 - 增强边缘厚度和对比度 */
.btnStyleAm::after {
  content: "";
  position: absolute;
  top: 1.8px;
  left: 1.8px;
  right: 1.8px;
  bottom: 1.8px;
  border-radius: 31px;
  border-top: 2px solid rgba(255, 255, 255, 0.6); /* 顶部边框更亮更粗 */
  border-left: 2px solid rgba(255, 255, 255, 0.5); /* 左侧边框增强 */
  border-right: 2px solid rgba(0, 0, 0, 0.2); /* 右侧边框加深 */
  border-bottom: 2px solid rgba(0, 0, 0, 0.3); /* 底部边框加深 */
  pointer-events: none;
}

/* 按钮文字 */
.btnStyleAm span {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 悬停效果 - 增强变化幅度 */
.btnStyleAm:hover {
  transform: perspective(1500px) rotateX(6deg) translateY(-5px);
  box-shadow: 0 3px 5px -10px rgba(0, 0, 0, 0.45),
    0 20px 25px -8px rgba(0, 0, 0, 0.3), 0 10px 15px -5px rgba(0, 0, 0, 0.2),
    inset 0 0 0 2px rgba(255, 255, 255, 0.4),
    inset 0 10px 15px 0 rgba(255, 255, 255, 0.6),
    inset 0 -22px 30px 0 rgba(0, 0, 0, 0.15);
}

/* 点击效果 */
.btnStyleAm:active {
  transform: perspective(1500px) rotateX(8deg) translateY(-4px);
  box-shadow: 0 18px 30px -8px rgba(0, 0, 0, 0.35),
    0 10px 15px -5px rgba(0, 0, 0, 0.2), 0 5px 8px -3px rgba(0, 0, 0, 0.15),
    inset 0 0 0 1.5px rgba(255, 255, 255, 0.3),
    inset 0 12px 18px 0 rgba(255, 255, 255, 0.5),
    inset 0 -12px 18px 0 rgba(0, 0, 0, 0.2);
}
</style>
